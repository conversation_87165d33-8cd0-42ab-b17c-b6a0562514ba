package com.dpw.ctms.move.fakers;

import com.dpw.ctms.move.dto.StateConfigDTO;
import com.dpw.ctms.move.dto.StateMachineTenantDTO;
import com.dpw.ctms.move.dto.StateTransitionHolderDTO;
import com.dpw.ctms.move.dto.TransitionConfigDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.github.javafaker.Faker;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Fakers {

    public static final Faker faker = new Faker();

    public static Map<String, StateMachineTenantDTO> createStateMachineTenantConfigMap(String tenantId) {
        StateMachineTenantDTO stateMachineTenantDTO = createStateMachineTenantDTO();
        Map<String, StateMachineTenantDTO> map = new HashMap<>();
        map.put(tenantId, stateMachineTenantDTO);
        return map;
    }

    public static Map<String, StateMachineTenantDTO> createStateMachineTenantConfigMapWithoutInitialState(String tenantId) {
        StateMachineTenantDTO stateMachineTenantDTO = createStateMachineTenantDTO();
        for (StateConfigDTO stateConfigDTO : stateMachineTenantDTO.getTask().getStates()) {
            stateConfigDTO.setIsInitial(false);
        }
        Map<String, StateMachineTenantDTO> map = new HashMap<>();
        map.put(tenantId, stateMachineTenantDTO);
        return map;
    }

    private static StateMachineTenantDTO createStateMachineTenantDTO() {
        return StateMachineTenantDTO.builder()
                .task(createStateTransitionHolder(StateMachineEntityType.TASK))
                .shipment(createStateTransitionHolder(StateMachineEntityType.SHIPMENT))
                .trip(createStateTransitionHolder(StateMachineEntityType.TRIP)).build();
    }

    private static StateTransitionHolderDTO createStateTransitionHolder(StateMachineEntityType stateMachineEntityType) {
        return StateTransitionHolderDTO.builder().states(createStateConfig(stateMachineEntityType))
                .transitions(createTransitionConfig(stateMachineEntityType)).build();
    }

    private static List<StateConfigDTO> createStateConfig(StateMachineEntityType stateMachineEntityType) {
        return switch (stateMachineEntityType) {
            case TASK -> List.of(
                    StateConfigDTO.builder().state("CREATED").isInitial(true).build(),
                    StateConfigDTO.builder().state("COMPLETED").isInitial(false).build(),
                    StateConfigDTO.builder().state("CLOSED").isInitial(false).build()
            );
            case SHIPMENT -> List.of(
                    StateConfigDTO.builder().state("ASSIGNED").isInitial(true).build(),
                    StateConfigDTO.builder().state("ALLOCATED").isInitial(false).build(),
                    StateConfigDTO.builder().state("IN_TRANSIT").isInitial(false).build(),
                    StateConfigDTO.builder().state("DELIVERED").isInitial(false).build(),
                    StateConfigDTO.builder().state("DELIVERED_WITH_EXCEPTION").isInitial(false).build(),
                    StateConfigDTO.builder().state("CANCELLED").isInitial(false).build()
            );
            case TRIP -> List.of(
                    StateConfigDTO.builder().state("CREATED").isInitial(true).build(),
                    StateConfigDTO.builder().state("IN_PROGRESS").isInitial(false).build(),
                    StateConfigDTO.builder().state("COMPLETED").isInitial(false).build(),
                    StateConfigDTO.builder().state("COMPLETED_WITH_EXCEPTIONS").isInitial(false).build(),
                    StateConfigDTO.builder().state("CANCELLED").isInitial(false).build(),
                    StateConfigDTO.builder().state("CLOSED").isInitial(false).build()
            );
        };
    }

    private static List<TransitionConfigDTO> createTransitionConfig(StateMachineEntityType stateMachineEntityType) {
        return switch (stateMachineEntityType) {
            case TASK -> List.of(
                    TransitionConfigDTO.builder().sourceState("CREATED").targetState("COMPLETED")
                            .event("TASK_COMPLETED")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("COMPLETED").targetState("CLOSED")
                            .event("TASK_CLOSED")
                            .guardId("TASK_TO_CLOSED_GUARD_ID")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("CREATED").targetState("CLOSED")
                            .event("TASK_CLOSED")
                            .guardId("TASK_TO_CLOSED_GUARD_ID")
                            .actionId("").build()
            );
            case SHIPMENT -> List.of(TransitionConfigDTO.builder().sourceState("ASSIGNED").targetState("ALLOCATED")
                            .event("RESOURCE_ALLOCATED")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("ALLOCATED").targetState("IN_TRANSIT")
                            .event("PICKED_UP")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("IN_TRANSIT").targetState("DELIVERED")
                            .event("DELIVERED")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("IN_TRANSIT").targetState("DELIVERED_WITH_EXCEPTION")
                            .event("DELIVERED_WITH_EXCEPTION")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("ASSIGNED").targetState("IN_TRANSIT")
                            .event("PICKED_UP")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("ASSIGNED").targetState("CANCELLED")
                            .event("CANCELLED")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("ALLOCATED").targetState("CANCELLED")
                            .event("CANCELLED")
                            .guardId("")
                            .actionId("").build()
            );
            case TRIP -> List.of(TransitionConfigDTO.builder().sourceState("CREATED").targetState("IN_PROGRESS")
                            .event("START_TRIP")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("IN_PROGRESS").targetState("COMPLETED")
                            .event("END_TRIP")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("IN_PROGRESS").targetState("COMPLETED_WITH_EXCEPTIONS")
                            .event("END_TRIP_WITH_EXCEPTION")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("COMPLETED").targetState("CLOSED")
                            .event("CLOSE_TRIP")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("COMPLETED_WITH_EXCEPTIONS").targetState("CLOSED")
                            .event("CLOSE_TRIP")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("CREATED").targetState("CANCELLED")
                            .event("CANCEL_TRIP")
                            .guardId("")
                            .actionId("").build()
            );
        };
    }

    public static Task createTask() {
        return Task.builder().code(faker.lorem().sentence())
                .externalTaskMasterCode(faker.lorem().sentence())
                .sequence(1)
                .expectedStartAt(System.currentTimeMillis())
                .expectedEndAt(System.currentTimeMillis())
                .status(TaskStatus.CREATED).build();
    }

    public static Shipment createShipment() {
        return Shipment.builder()
                .code(faker.lorem().characters())
                .status(ShipmentStatus.ASSIGNED)
                .actualPickupAt(null)
                .actualDeliveryAt(null)
                .expectedDeliveryAt(System.currentTimeMillis())
                .expectedPickupAt(System.currentTimeMillis())
                .build();
    }

    public static Trip createTrip() {
        return new Trip(
                faker.lorem().characters(), // code
                TripStatus.CREATED, // status
                faker.lorem().word(), // externalOriginLocationCode
                faker.lorem().word(), // externalDestinationLocationCode
                System.currentTimeMillis(), // expectedStartAt
                System.currentTimeMillis(), // expectedEndAt
                null,
                null, // actualEndAt
                null, // details (JsonNode)
                null, // shipments (Set<Shipment>)
                null, // stops (Set<Stop>)
                null, // vehicleResource (VehicleResource)
                null, // trailerResources (Set<TrailerResource>)
                null, // vehicleOperatorResources (Set<VehicleOperatorResource>)
                null, // transportOrder (TransportOrder)
                null  // exceptions (Set<Exception>)
        );
    }
}

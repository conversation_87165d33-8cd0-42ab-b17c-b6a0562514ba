package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StopStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.repository.StopRepository;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.response.ShipmentViewResponse;
import com.dpw.ctms.move.testcontainers.TestDatabaseManager;
import com.dpw.tmsutils.threadlocal.TenantContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

import static com.dpw.tmsutils.constant.RequestConstants.TENANT_HEADER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class ShipmentViewIntegrationTest extends IntegrationTestBase {

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private StopRepository stopRepository;

    @Autowired
    private TransportOrderRepository transportOrderRepository;

    private static final String SHIPMENT_CODE = "SHIP-001";
    private static final String TRANSPORT_ORDER_CODE = "TO-001";
    private static final String ORIGIN_STOP_CODE = "ORIGIN-STOP-001";
    private static final String DESTINATION_STOP_CODE = "DEST-STOP-001";
    private static final String EXTERNAL_CONSIGNMENT_ID = "CONSIGN-001";
    private static final String EXTERNAL_CUSTOMER_ORDER_ID = "CO-001";
    private static final String ORIGIN_LOCATION_CODE = "ORIGIN-LOC-001";
    private static final String DESTINATION_LOCATION_CODE = "DEST-LOC-001";

    @BeforeEach
    void setUp() {
        TenantContext.setCurrentTenant("CFR");
        TestDatabaseManager.cleanupCfrSchema();
    }

    @AfterEach
    void cleanup() {
        TenantContext.setCurrentTenant("CFR");
        try {
            shipmentRepository.deleteAll();
            stopRepository.deleteAll();
            transportOrderRepository.deleteAll();
        } catch (Exception e) {
            TestDatabaseManager.cleanupCfrSchema();
        }
        TenantContext.clear();
    }

    @Test
    @Transactional
    void getShipmentView_ShouldReturnShipmentWithAllRelations() throws Exception {
        // Given
        setupTestData();

        // When & Then
        MvcResult result = mockMvc.perform(post("/v1/shipments/{shipmentCode}/view", SHIPMENT_CODE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .header(TENANT_HEADER, "CFR"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.shipmentCode").value(SHIPMENT_CODE))
                .andExpect(jsonPath("$.status.value").value(ShipmentStatus.ASSIGNED.name()))
                .andExpect(jsonPath("$.status.label").value(ShipmentStatus.ASSIGNED.getDisplayName()))
                .andExpect(jsonPath("$.originStop.code").value(ORIGIN_STOP_CODE))
                .andExpect(jsonPath("$.originStop.externalLocationCode").value(ORIGIN_LOCATION_CODE))
                .andExpect(jsonPath("$.originStop.status.value").value(StopStatus.PLANNED.name()))
                .andExpect(jsonPath("$.originStop.status.label").value(StopStatus.PLANNED.getDisplayName()))
                .andExpect(jsonPath("$.destinationStop.code").value(DESTINATION_STOP_CODE))
                .andExpect(jsonPath("$.destinationStop.externalLocationCode").value(DESTINATION_LOCATION_CODE))
                .andExpect(jsonPath("$.destinationStop.status.value").value(StopStatus.PLANNED.name()))
                .andExpect(jsonPath("$.destinationStop.status.label").value(StopStatus.PLANNED.getDisplayName()))
                .andExpect(jsonPath("$.customerOrder.customerOrderId").value(EXTERNAL_CUSTOMER_ORDER_ID))
                .andExpect(jsonPath("$.customerOrder.consignmentId").value(EXTERNAL_CONSIGNMENT_ID))
                .andExpect(jsonPath("$.transportOrder.transportOrderCode").value(TRANSPORT_ORDER_CODE))
                .andExpect(jsonPath("$.expectedTimes.startAt").exists())
                .andExpect(jsonPath("$.expectedTimes.endAt").exists())
                .andExpect(jsonPath("$.actualTimes.startAt").doesNotExist())
                .andExpect(jsonPath("$.actualTimes.endAt").doesNotExist())
                .andExpect(jsonPath("$.volume").value(100.5))
                .andExpect(jsonPath("$.volumeUom").value("L"))
                .andExpect(jsonPath("$.weight").value(50.25))
                .andExpect(jsonPath("$.weightUom").value("KG"))
                .andReturn();

        // Parse response and verify structure
        ShipmentViewResponse response = objectMapper.readValue(result.getResponse().getContentAsString(), ShipmentViewResponse.class);
        assertNotNull(response);
        assertEquals(SHIPMENT_CODE, response.getShipmentCode());
        assertEquals(ShipmentStatus.ASSIGNED.getDisplayName(), response.getStatus().getLabel());
        assertEquals(ShipmentStatus.ASSIGNED.name(), response.getStatus().getValue());
    }

    @Test
    void getShipmentView_WhenShipmentDoesNotExist_ShouldReturnNotFound() throws Exception {
        // When & Then
        mockMvc.perform(post("/v1/shipments/{shipmentCode}/view", "NON_EXISTENT_SHIPMENT")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header(TENANT_HEADER, "CFR"))
                .andExpect(status().isUnprocessableEntity())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.errorCode").value("INVALID_REQUEST"))
                .andExpect(jsonPath("$.message").value("Shipment not found with code: NON_EXISTENT_SHIPMENT"));
    }

    @Test
    @Transactional
    void getShipmentView_WithNullRelatedEntities_ShouldHandleNullValues() throws Exception {
        // Given - Create shipment with minimal data (null transport order, stops)
        setupMinimalTestData();

        // When & Then
        MvcResult result = mockMvc.perform(post("/v1/shipments/{shipmentCode}/view", "SHIP-MINIMAL")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header(TENANT_HEADER, "CFR"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.shipmentCode").value("SHIP-MINIMAL"))
                .andExpect(jsonPath("$.status.value").value(ShipmentStatus.IN_TRANSIT.name()))
                .andExpect(jsonPath("$.originStop").doesNotExist())
                .andExpect(jsonPath("$.destinationStop").doesNotExist())
                .andExpect(jsonPath("$.transportOrder").doesNotExist())
                .andExpect(jsonPath("$.customerOrder.customerOrderId").value("CO-MINIMAL"))
                .andExpect(jsonPath("$.customerOrder.consignmentId").value("CONSIGN-MINIMAL"))
                .andReturn();

        // Verify null handling in mapper
        ShipmentViewResponse response = objectMapper.readValue(result.getResponse().getContentAsString(), ShipmentViewResponse.class);
        assertNotNull(response);
        assertNull(response.getOriginStop());
        assertNull(response.getDestinationStop());
        assertNull(response.getTransportOrder());
    }

    private void setupTestData() {
        long currentTime = System.currentTimeMillis();

        // Create Transport Order
        TransportOrder transportOrder = new TransportOrder();
        transportOrder.setCode(TRANSPORT_ORDER_CODE);
        transportOrder.setStatus(TransportOrderStatus.ASSIGNED);
        transportOrder.setCreatedAt(currentTime);
        transportOrder.setCreatedBy("test-user");
        transportOrder = transportOrderRepository.save(transportOrder);

        // Create Origin Stop
        Stop originStop = new Stop();
        originStop.setCode(ORIGIN_STOP_CODE);
        originStop.setExternalLocationCode(ORIGIN_LOCATION_CODE);
        originStop.setStatus(StopStatus.PLANNED);
        originStop.setSequence(1);
        originStop.setCreatedAt(currentTime);
        originStop.setCreatedBy("test-user");
        originStop = stopRepository.save(originStop);

        // Create Destination Stop
        Stop destinationStop = new Stop();
        destinationStop.setCode(DESTINATION_STOP_CODE);
        destinationStop.setExternalLocationCode(DESTINATION_LOCATION_CODE);
        destinationStop.setStatus(StopStatus.PLANNED);
        destinationStop.setSequence(2);
        destinationStop.setCreatedAt(currentTime);
        destinationStop.setCreatedBy("test-user");
        destinationStop = stopRepository.save(destinationStop);

        // Create Shipment with all relations
        Shipment shipment = new Shipment();
        shipment.setCode(SHIPMENT_CODE);
        shipment.setStatus(ShipmentStatus.ASSIGNED);
        shipment.setTransportOrder(transportOrder);
        shipment.setOriginStop(originStop);
        shipment.setDestinationStop(destinationStop);
        shipment.setExternalConsignmentId(EXTERNAL_CONSIGNMENT_ID);
        shipment.setExternalCustomerOrderId(EXTERNAL_CUSTOMER_ORDER_ID);
        shipment.setExpectedPickupAt(currentTime);
        shipment.setExpectedDeliveryAt(currentTime + 3600000L);
        shipment.setVolume(BigDecimal.valueOf(100.5));
        shipment.setVolumeUom("L");
        shipment.setWeight(BigDecimal.valueOf(50.25));
        shipment.setWeightUom("KG");
        shipment.setCreatedAt(currentTime);
        shipment.setCreatedBy("test-user");
        shipmentRepository.save(shipment);
    }

    @Test
    @Transactional
    void getShipmentView_WithActualTimes_ShouldMapActualTimesCorrectly() throws Exception {
        // Given - Create shipment with actual times and different status
        setupShipmentWithActualTimes();

        // When & Then
        MvcResult result = mockMvc.perform(post("/v1/shipments/{shipmentCode}/view", "SHIP-ACTUAL")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header(TENANT_HEADER, "CFR"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.shipmentCode").value("SHIP-ACTUAL"))
                .andExpect(jsonPath("$.status.value").value(ShipmentStatus.DELIVERED.name()))
                .andExpect(jsonPath("$.status.label").value(ShipmentStatus.DELIVERED.getDisplayName()))
                .andExpect(jsonPath("$.actualTimes.startAt").exists())
                .andExpect(jsonPath("$.actualTimes.endAt").exists())
                .andExpect(jsonPath("$.expectedTimes.startAt").exists())
                .andExpect(jsonPath("$.expectedTimes.endAt").exists())
                .andReturn();

        // Verify actual times are mapped correctly
        ShipmentViewResponse response = objectMapper.readValue(result.getResponse().getContentAsString(), ShipmentViewResponse.class);
        assertNotNull(response.getActualTimes());
        assertNotNull(response.getActualTimes().getStartAt());
        assertNotNull(response.getActualTimes().getEndAt());
    }

    private void setupMinimalTestData() {
        long currentTime = System.currentTimeMillis();

        // Create Shipment with minimal data (no transport order, no stops)
        Shipment shipment = new Shipment();
        shipment.setCode("SHIP-MINIMAL");
        shipment.setStatus(ShipmentStatus.IN_TRANSIT);
        shipment.setExternalConsignmentId("CONSIGN-MINIMAL");
        shipment.setExternalCustomerOrderId("CO-MINIMAL");
        shipment.setExpectedPickupAt(currentTime);
        shipment.setExpectedDeliveryAt(currentTime + 3600000L);
        shipment.setCreatedAt(currentTime);
        shipment.setCreatedBy("test-user");
        shipmentRepository.save(shipment);
    }

    private void setupShipmentWithActualTimes() {
        long currentTime = System.currentTimeMillis();

        // Create Shipment with actual times
        Shipment shipment = new Shipment();
        shipment.setCode("SHIP-ACTUAL");
        shipment.setStatus(ShipmentStatus.DELIVERED);
        shipment.setExternalConsignmentId("CONSIGN-ACTUAL");
        shipment.setExternalCustomerOrderId("CO-ACTUAL");
        shipment.setExpectedPickupAt(currentTime);
        shipment.setExpectedDeliveryAt(currentTime + 3600000L);
        shipment.setActualPickupAt(currentTime + 1800000L); // 30 minutes later
        shipment.setActualDeliveryAt(currentTime + 5400000L); // 90 minutes later
        shipment.setCreatedAt(currentTime);
        shipment.setCreatedBy("test-user");
        shipmentRepository.save(shipment);
    }
}

package com.dpw.ctms.move.request.taskmanager;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TaskInstanceRegistrationRequest {
    List<TaskInstanceRegisterDetailsRequest> tasks;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskInstanceRegisterDetailsRequest {
        String extTaskTransactionCode;
        String extTaskMasterCode;
        Long taskVersion;
        String assignedToCode;
        String assignedToType;
        String groupingIdentifier;
        String tenantCode;
        String tenantServiceCode;
        String hierarchyIdentifier;
        ExpectedDateRange expectedDateRange;
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExpectedDateRange {
        long start;
        long end;
    }
}

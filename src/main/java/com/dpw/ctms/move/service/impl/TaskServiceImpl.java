package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.integrations.adapter.TaskServiceAdapter;
import com.dpw.ctms.move.mapper.TaskInstanceRegistrationRequestMapper;
import com.dpw.ctms.move.repository.TaskRepository;
import com.dpw.ctms.move.request.taskmanager.TaskInstanceRegistrationRequest;
import com.dpw.ctms.move.request.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.tmsutils.annotation.MethodLog;
import com.dpw.tmsutils.exception.TMSException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TASK_CODE;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TASK_ID;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;

@Service
@RequiredArgsConstructor
@Slf4j
public class TaskServiceImpl implements ITaskService {

    private final TaskInstanceRegistrationRequestMapper taskInstanceRegistrationRequestMapper;
    private final TaskRepository taskRepository;
    private final TaskServiceAdapter taskServiceAdapter;

    @Override
    public Task findTaskById(Long taskId) {
        return taskRepository.findById(taskId).orElseThrow(() -> {
            log.error("Task id {} not found", taskId);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_TASK_ID, taskId)
            );
        });
    }

    @Override
    public Task saveTask(Task task) {
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @MethodLog
    public void instantiateTasks(List<Task> taskList) {
        TaskInstanceRegistrationRequest taskInstanceRegistrationRequest =
                taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(taskList);
        List<TaskInstanceRegistrationResponse> listResponseResponseEntity =
                taskServiceAdapter.registerTaskInstance(taskInstanceRegistrationRequest);
        Map<String, Task> taskByCode = taskList.stream()
                .collect(Collectors.toMap(Task::getCode, Function.identity()));
        listResponseResponseEntity.forEach(response -> {
            Optional.ofNullable(taskByCode.get(response.getExtTaskTransactionCode()))
                    .ifPresent(task -> task.setExternalTaskRegistrationCode(response.getTaskRegistrationCode()));
        });
        taskRepository.saveAll(taskList);
    }

    @Override
    public Task findTaskByCode(String taskCode) {
        return taskRepository.findByCode(taskCode).orElseThrow(() -> {
            log.error("Task with code {} not found", taskCode);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_TASK_CODE, taskCode)
            );
        });
    }

}

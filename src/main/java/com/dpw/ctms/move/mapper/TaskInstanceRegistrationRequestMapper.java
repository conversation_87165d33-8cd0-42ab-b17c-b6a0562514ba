package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.constants.TaskServiceConstants;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.request.taskmanager.TaskInstanceRegistrationRequest;
import org.mapstruct.*;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TaskInstanceRegistrationRequestMapper {

    default TaskInstanceRegistrationRequest toTaskInstanceRegisterRequest(List<Task> tasks) {
        TaskInstanceRegistrationRequest request = new TaskInstanceRegistrationRequest();
        request.setTasks(toTaskInstanceRegisterDetailsRequestList(tasks));
        return request;
    }

    List<TaskInstanceRegistrationRequest.TaskInstanceRegisterDetailsRequest> toTaskInstanceRegisterDetailsRequestList(List<Task> tasks);

    @Mappings({
        @Mapping(target = "extTaskTransactionCode", source = "code"),
        @Mapping(target = "extTaskMasterCode", source = "externalTaskMasterCode"),
        @Mapping(target = "expectedDateRange", expression = "java(toExpectedDateRange(task.getExpectedStartAt(), task.getExpectedEndAt()))"),
        @Mapping(target = "tenantCode", constant = TaskServiceConstants.DEFAULT_TENANT_CODE),
        @Mapping(target = "tenantServiceCode", constant = TaskServiceConstants.DEFAULT_TENANT_SERVICE_CODE),
    })
    TaskInstanceRegistrationRequest.TaskInstanceRegisterDetailsRequest toTaskInstanceRegisterDetailsRequest(Task task);


    default TaskInstanceRegistrationRequest.ExpectedDateRange toExpectedDateRange(Long start, Long end) {
        if (start == null && end == null) return null;
        TaskInstanceRegistrationRequest.ExpectedDateRange range = new TaskInstanceRegistrationRequest.ExpectedDateRange();
        range.setStart(start != null ? start : 0L);
        range.setEnd(end != null ? end : 0L);
        return range;
    }
}
